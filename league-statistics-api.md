# League Statistics API Documentation

This document describes the League Statistics API endpoints, response structure, and usage guidelines.

## Endpoints

### Get All Leagues

```
GET /api/v1/statistics/league/details
```

Returns a list of leagues from the current season with statistics information.

## Testing the endpoint

```
curl -i --location --header "Accept: application/json" "https://hbl-sta.pass-consulting.com/api/v1/statistics/league/details?changedAfter=2025-05-05T22:30:00&page=0&size=300" --header "apiKey: 01939200bf6e7d129210a75194eaeb67" --header "magic-token: 9619800527"
 ```

## Request Parameters

| Parameter    | Type               | Required | Description                                           |
|--------------|--------------------|---------|----------------------------------------------------|
| changedAfter | ISO 8601 DateTime  | No      | Filter leagues modified after this date              |
| page         | Integer            | No      | Page number ( zero-based, defaults to 0 if not set )            |
| size         | Integer            | No      | Page size ( defaults to 20, if not set ) ( max to 3000, example : if size set to 3200 it will be reset to 3000 )       |

## Response Format

The response is a JSON array of league statistics objects.

### HTTP Headers

| Header               | Description                                           |
|----------------------|-------------------------------------------------------|
| X-Rate-Limit-Reset   | Timestamp when rate limit will reset (only sent when rate limit is exceeded) |
| Content-Type         | application/json                                      |

## Response Structure

### League Statistics Response

The API returns an array of league statistics objects with the following structure:

```json
[
  {
    "id": "7d87907d-6087-4297-994a-59fcb4b444b1",
    "name": "Avengers",
    "totalMemberCount": 2,
    "totalLeagueCount": 2
  },
  {
    "id": "9dd1b7ab-a0e8-4f86-970f-2bad1b797bb1",
    "name": "Avengers123",
    "totalMemberCount": 1,
    "totalLeagueCount": 2
  }
]
```

## Field Descriptions

| Field | Type | Description |
|-------|------|-------------|
| `id` | String (UUID) | Unique identifier for the league |
| `name` | String | Name of the league (max 256 characters) |
| `totalMemberCount` | Integer | Total number of members in the league |
| `totalLeagueCount` | Integer | Total number of leagues in the current season |

## Pagination

The API supports pagination using standard Spring pagination parameters:

- `page`: Zero-based page index (0 is the first page)
- `size`: Size of the page to be returned

Example request with pagination:

```
GET /api/v1/statistics/league/details?page=0&size=10&sort=name,asc
```

Note: There is a maximum page size limit configured on the server (default: 3000). If you request a larger page size, it will be capped to this maximum value.

## Filtering

### Changed After Filter

You can filter leagues that have been modified after a specific date/time using the `changedAfter` parameter:

```
GET /api/v1/statistics/league/details?changedAfter=2023-05-15T10:30:00
```

The date format must be ISO 8601 (e.g., `2023-05-15T10:30:00`).

This filter will return leagues that have been:
- Created after the specified date/time
- Modified after the specified date/time
- Had membership changes after the specified date/time

## Rate Limiting

The API implements rate limiting to prevent abuse. If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

Rate limiting is based on:
- Client identifier
- Request URL
- Time window (configurable, default: 15 minutes)

### Rate Limit Headers

When rate limiting is active, the following header is included in responses:

- `X-Rate-Limit-Reset`: Unix timestamp (in seconds) when the rate limit will reset

## Error Responses

### 401 Unauthorized

Returned when authentication fails or is missing.

### 404 Not Found

Returned when the current season cannot be found.

```json
"Current season not found: No active season exists"
```

### 429 Too Many Requests

Returned when rate limit is exceeded.

```json
"Rate limit exceeded. Try again after 2023-06-01T14:30:00"
```

## Examples

### Basic Request

```
GET /api/v1/statistics/league/details
```

### Paginated Request

```
GET /api/v1/statistics/league/details?changedAfter=2023-05-15T10:30:00&page=0&size=20
```


