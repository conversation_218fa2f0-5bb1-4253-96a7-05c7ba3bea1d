package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueStatisticsResponseDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.LeagueStatisticsHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Slf4j
public class LeagueStatisticsService {

    private final LeagueStatisticsHandler leagueStatisticsHandler;

    public LeagueStatisticsService(LeagueStatisticsHandler leagueStatisticsHandler) {
        this.leagueStatisticsHandler = leagueStatisticsHandler;
    }

    @Transactional(readOnly = true)
    public LeagueStatisticsResponseDto getAllLeagueStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, RateLimitExceededException, EntityNotExistException {
        return leagueStatisticsHandler.getAllLeagueStatistics(changedAfter, pageable, requestUrl, externalClient);
    }
}
