# Handball Manager API Documentation Guide

This guide outlines best practices for documenting the Handball Manager API using OpenAPI/Swagger and provides integration options with Zuplo.

## Improving Existing Documentation

Enhance your OpenAPI configuration in `OpenApiConfig.java`:

```java
@Bean
public OpenAPI handballManagerOpenAPI() {
    return new OpenAPI()
            .info(new Info()
                    .title("HBL handball manager API")
                    .version("1.0")
                    .description("Internal App Backend")
                    .contact(new Contact()
                            .name("HBL Development Team")
                            .email("<EMAIL>"))
                    .license(new License().name("Proprietary"))
            );
}
```

## Best Practices for Endpoint Documentation

Follow this pattern for all controllers:

```java
@RestController
@RequestMapping(ApiConstants.STAT_API + "/league")
@Validated
@Tag(name = "statistics", description = "API for league statistics data")
@Slf4j
public class LeagueStatisticsController {

    @Operation(summary = "Get all leagues from the current season")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                    array = @ArraySchema(schema = @Schema(implementation = LeagueStatisticsResponseDto.class)))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = { @Content }),
            // Other responses...
    })
    @GetMapping(value = "/details", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<LeagueStatisticsResponseDto> getAllLeagues(/* params */) {
        // Implementation
    }
}
```

### Key Annotations

- `@Tag` - Group controllers by functionality
- `@Operation` - Document endpoint purpose
- `@ApiResponse` - Document all possible responses
- `@Parameter` - Document request parameters
- `@Schema` - Document model properties

## Zuplo Integration

To integrate with Zuplo's documentation tools:

1. Configure SpringDoc in `application.yml`:

```yaml
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
  packages-to-scan: com.pass.hbl.manager.backend.restservice.controller
  cache:
    disabled: true
```

2. Export your OpenAPI spec by accessing the `/api-docs` endpoint
3. Import this spec into Zuplo's Zudoku

## Implementation Steps

1. Create a dedicated controller for API documentation access:

```java
@Controller
public class ApiDocController {
    @Value("${springdoc.swagger-ui.path}")
    private String swaggerPath;

    @GetMapping("/docs")
    public RedirectView redirectToSwaggerUi() {
        return new RedirectView(swaggerPath);
    }
}
```

2. Add security scheme documentation:

```java
.components(new Components()
        .addSecuritySchemes("bearerAuth",
                new SecurityScheme()
                        .name("bearerAuth")
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
        )
        .addSecuritySchemes("apiKey",
                new SecurityScheme()
                        .name("apiKey")
                        .type(SecurityScheme.Type.APIKEY)
                        .in(SecurityScheme.In.QUERY)
        )
)
```

## Additional Tips

- Document DTOs thoroughly with `@Schema` annotations
- Group endpoints logically with consistent tag names
- Include examples where possible using `@Schema(example = "...")`
- Document error responses with appropriate status codes