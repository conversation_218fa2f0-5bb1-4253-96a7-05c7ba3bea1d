package com.pass.hbl.manager.backend.restservice.controller;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueStatisticsResponseDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.LeagueStatisticsService;
import com.pass.hbl.manager.backend.restservice.util.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;

import static com.pass.hbl.manager.backend.persistence.util.Constants.DEFAULT_LEAGUE_STAT_EXTERNAL_CLIENT;
import static com.pass.hbl.manager.backend.persistence.util.Constants.X_RATE_LIMIT_RESET;

/**
 * Controller for the league statistics API endpoints
 * Provides endpoints for retrieving league statistics data
 * Secured with API key authentication
 */
@RestController
@RequestMapping(ApiConstants.STAT_API + "/league")
@Validated
@Tag(name = "statistics", description = "API for league statistics data")
@Slf4j
public class LeagueStatisticsController {

    private final LeagueStatisticsService leagueStatisticsService;

    public LeagueStatisticsController(LeagueStatisticsService leagueStatisticsService) {
        this.leagueStatisticsService = leagueStatisticsService;
    }


    @Operation(summary = "Get all leagues from the current season")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success",
                    content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = LeagueStatisticsResponseDto.class))}),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = { @Content }),
            @ApiResponse(responseCode = "404", description = "Current season not found", content = { @Content }),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded", content = { @Content })
    })
    @GetMapping(value = "/details", produces = MediaType.APPLICATION_JSON_VALUE)
    public LeagueStatisticsResponseDto getAllLeagues(
            @Parameter(description = "Filter leagues modified after this date")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            @ParameterObject Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws InvalidOperationException, FormatException, IOException {
        String requestUrl = request.getRequestURI();
        String externalClient = DEFAULT_LEAGUE_STAT_EXTERNAL_CLIENT;

        log.info("Received request for all league statistics from client: {} with URL: {}", externalClient, requestUrl);

        try {
            return leagueStatisticsService.getAllLeagueStatistics(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (EntityNotExistException entityNotExistException) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.getWriter().write("Current season not found: " + entityNotExistException.getMessage());
            return null;
        }
    }
}
