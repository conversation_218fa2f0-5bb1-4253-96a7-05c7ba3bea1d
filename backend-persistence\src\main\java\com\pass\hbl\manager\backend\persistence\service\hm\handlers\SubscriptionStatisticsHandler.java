package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.SubscriptionStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.SubscriptionStatisticsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmSubscriptionInfoRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SubscriptionStatisticsHandler {

    private final HmSubscriptionInfoRepository subscriptionInfoRepository;
    private final HmClientRequestRepository clientRequestRepository;
    private final ParameterService parameterService;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_SUBSCRIPTION_STAT_PAGE_SIZE;
    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES;
    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES;
    
    /**
     * Cache for subscription IDs to support pagination = Triple <left,Middle,right> represents the subscription ids who have
     * performed any change (related to the requested data structure e.g. subscription updates) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private final Map<String, Triple<LocalDateTime, LocalDateTime, List<UUID>>> subscriptionIdCache = new HashMap<>();

    @SneakyThrows
    @Transactional(readOnly = true)
    public List<SubscriptionStatisticsDto> getAllSubscriptionStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws RateLimitExceededException {

        log.info("getAllSubscriptionStatistics: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);

        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);

        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);

        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);

        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_SUBSCRIPTION_STAT_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_SUBSCRIPTION_STAT_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        // Validate page size
        if (pageable.getPageSize() > maxStatPageSize) {
            throw new IllegalArgumentException("Page size cannot exceed " + maxStatPageSize);
        }

        // Get cached data or fetch new data
        Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheData = getCachedSubscriptionIds(changedAfter, externalClient);

        // Calculate pagination
        int totalSubscriptions = cacheData.getRight().size();
        int startIndex = pageable.getPageNumber() * pageable.getPageSize();
        int endIndex = Math.min(startIndex + pageable.getPageSize(), totalSubscriptions);

        if (startIndex >= totalSubscriptions) {
            return new ArrayList<>();
        }

        List<UUID> pageSubscriptionIds = cacheData.getRight().subList(startIndex, endIndex);

        List<SubscriptionStatisticsBasicDO> subscriptionsBasicData = subscriptionInfoRepository.findSubscriptionStatisticsByIdIn(pageSubscriptionIds);

        return subscriptionsBasicData.stream()
                .map(subscriptionData -> {
                    SubscriptionStatisticsDto dto = new SubscriptionStatisticsDto();
                    dto.setUserSsoId(subscriptionData.getUserSsoId());
                    dto.setSubscriptionId(subscriptionData.getSubscriptionId());
                    dto.setSubscriptionStartDate(subscriptionData.getStartDate());
                    dto.setSubscriptionEndDate(subscriptionData.getEndDate());
                    dto.setSubscriptionType(subscriptionData.getSubscriptionType());
                    dto.setSubscriptionPeriod(subscriptionData.getSubscriptionPeriod());
                    dto.setPlatform(subscriptionData.getPlatform());
                    dto.setPaymentId(subscriptionData.getLastPaymentId());
                    dto.setPaymentAmount(subscriptionData.getLastPaymentAmount());
                    dto.setPaymentDate(subscriptionData.getLastPaymentDate());
                    return dto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Triple<LocalDateTime, LocalDateTime, List<UUID>> getCachedSubscriptionIds(LocalDateTime changedAfter, String externalClient) {
        String cacheKey = generateCacheKey(changedAfter, externalClient);
        
        Triple<LocalDateTime, LocalDateTime, List<UUID>> cachedData = subscriptionIdCache.get(cacheKey);
        
        // Check if cache is valid
        if (nonNull(cachedData) && isCacheValid(cachedData.getLeft())) {
            log.info("Using cached subscription IDs for key: {}", cacheKey);
            return cachedData;
        }

        // Fetch fresh data
        log.info("Fetching fresh subscription data for changedAfter: {}", changedAfter);
        List<SubscriptionStatisticsBasicDO> subscriptions = subscriptionInfoRepository.findSubscriptionStatistics(changedAfter);
        
        List<UUID> subscriptionIds = subscriptions.stream()
                .map(subscription -> UUID.fromString(subscription.getId()))
                .collect(Collectors.toList());

        LocalDateTime now = LocalDateTime.now();
        Triple<LocalDateTime, LocalDateTime, List<UUID>> newCacheData = Triple.of(now, changedAfter, subscriptionIds);
        
        subscriptionIdCache.put(cacheKey, newCacheData);
        
        log.info("Cached {} subscription IDs for key: {}", subscriptionIds.size(), cacheKey);
        return newCacheData;
    }

    private String generateCacheKey(LocalDateTime changedAfter, String externalClient) {
        return externalClient + "_" + (isNull(changedAfter) ? "null" : changedAfter.toString());
    }

    private boolean isCacheValid(LocalDateTime cacheTime) {
        return cacheTime.plusMinutes(forceRefreshCacheAfterMinutes).isAfter(LocalDateTime.now());
    }
}

