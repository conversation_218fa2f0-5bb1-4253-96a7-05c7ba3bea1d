package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;
import java.util.UUID;

public interface HmSeasonRepository extends PagingAndSortingRepository<HmSeason, UUID> {
    @Query(value = "SELECT * FROM hm.season WHERE start_date <= CURRENT_DATE and end_date >= CURRENT_DATE and deleted = false limit 1", nativeQuery = true)
    Optional<HmSeason> findCurrentSeason();

    @Query(value = "SELECT * FROM hm.season WHERE end_date < CURRENT_DATE and deleted = false ORDER BY end_date DESC limit 1", nativeQuery = true)
    Optional<HmSeason> findPreviousSeason();

    @Query(value = "SELECT id FROM hm.season WHERE end_date < CURRENT_DATE ORDER BY end_date DESC limit 1", nativeQuery = true)
    Optional<String> findPreviousSeasonId();

    Optional<HmSeason> findByName(String name);

    boolean existsByName(String name);
}
