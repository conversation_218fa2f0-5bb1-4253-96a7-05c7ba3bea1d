package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "subscription_info", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.subscription_info SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmSubscriptionInfo extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile user;

    @NotNull
    @Column(name = "subscription_id")
    private String subscriptionId;

    @NotNull
    @Column(name = "subscription_type")
    @Enumerated(EnumType.STRING)
    private SubscriptionType subscriptionType;

    @NotNull
    @Column(name = "subscription_period")
    @Enumerated(EnumType.STRING)
    private SubscriptionPeriod subscriptionPeriod;

    @NotNull
    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "last_payment_amount")
    private BigDecimal lastPaymentAmount;

    @Column(name = "last_payment_date")
    private LocalDateTime lastPaymentDate;

    @Column(name = "last_payment_id")
    private String lastPaymentId;

    @Column(name = "platform")
    @Enumerated(EnumType.STRING)
    private Platform platform;

    public enum SubscriptionType {
        PLUS,
        WORLD
    }

    public enum SubscriptionPeriod {
        MONTHLY,
        YEARLY
    }

    public enum Platform {
        GOOGLE,
        APPLE
    }
}