package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleJWSTransactionDecodedPayloadDo {

//    all decoded payload that contains transaction information can be found here
//    https://developer.apple.com/documentation/appstoreserverapi/jwstransactiondecodedpayload

    @Schema(description = "A UUID that associates the transaction with a user on your service.")
    private UUID appAccountToken;

    @Schema(description = "The bundle identifier of the app")
    private String bundleId;

    @Schema(description = "The server environment, either sandbox or production.")
    private String environment;

    @Schema(description = "The UNIX time, in milliseconds, the subscription expires or renews.")
    private Long expiresDate;

    @Schema(description = "A string that describes whether the transaction was purchased by the user, or is available to them through Family Sharing.")
    private String inAppOwnershipType;

    @Schema(description = "The UNIX time, in milliseconds, that represents the purchase date of the original transaction identifier")
    private Long originalPurchaseDate;

    @Schema(description = "The transaction identifier of the original purchase")
    private String originalTransactionId;

    @Schema(description = "The product identifier of the in-app purchase.")
    private String productId;

    @Schema(description = "The time that the App Store charged the user’s account for a purchase, a restored product, a subscription, or a subscription renewal after a lapse.")
    private Long purchaseDate;

    @Schema(description = "The number of consumable products the user purchased.")
    private String quantity;

    @Schema(description = "The unique identifier for a transaction such as an in-app purchase, restored purchase, or subscription renewal.")
    private String transactionId;

    @Schema(description = "The product type of the in-app purchase.")
    private String type;

    @Schema(description = "The unique identifier of subscription purchase events across devices, including subscription renewals.")
    private String webOrderLineItemId;

    @Schema(description = "An integer value that represents the price multiplied by 1000 of the in-app purchase or subscription offer you configured in App Store Connect and that the system records at the time of the purchase")
    private Integer price;

    @JsonCreator
    public AppleJWSTransactionDecodedPayloadDo() {
    }
}
