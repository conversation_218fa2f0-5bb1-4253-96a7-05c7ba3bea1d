package com.pass.hbl.manager.backend.persistence.service.hm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.pass.hbl.manager.backend.persistence.domain.hm.GoogleDeveloperNotificationDo;
import com.pass.hbl.manager.backend.persistence.domain.hm.GoogleNotificationType;
import com.pass.hbl.manager.backend.persistence.domain.hm.GoogleSubscriptionNotificationDo;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPendingPurchaseNotification;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSubscriptionInfo;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserPurchaseNotification;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.TechnicalException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPendingPurchaseNotificationRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserPurchaseNotificationRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Objects;
import java.util.Optional;

import static com.pass.hbl.manager.backend.persistence.exception.TechnicalException.Type.DATABASE_UPDATE_FAILED;
import static com.pass.hbl.manager.backend.persistence.exception.TechnicalException.Type.GOOGLE_IN_APP_PURCHASES;
import static java.util.Objects.isNull;

@Slf4j
@Service
@Transactional
public class AndroidNotificationService {

    private final UserProfileService userProfileService;
    private final LogMessageService logMessageService;
    private final SubscriptionInfoService subscriptionInfoService;

    private final HmPendingPurchaseNotificationRepository pendingPurchaseNotificationRepository;
    private final HmUserPurchaseNotificationRepository userPurchaseNotificationRepository;

    public AndroidNotificationService(UserProfileService userProfileService, LogMessageService logMessageService, SubscriptionInfoService subscriptionInfoService, HmPendingPurchaseNotificationRepository pendingPurchaseNotificationRepository, HmUserPurchaseNotificationRepository userPurchaseNotificationRepository) {
        this.userProfileService = userProfileService;
        this.logMessageService = logMessageService;
        this.subscriptionInfoService = subscriptionInfoService;
        this.pendingPurchaseNotificationRepository = pendingPurchaseNotificationRepository;
        this.userPurchaseNotificationRepository = userPurchaseNotificationRepository;
    }

    public void receive(@RequestBody String body, AndroidPublisher publisher) {
        log.info("Received Google In-app purchase notification: " + body);
        // Example: {"message":{"data":"eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJjb20ucGFzcy5oYW5kYmFsbCIsImV2ZW50VGltZU1pbGxpcyI6IjE2Nzg3ODYzMDc2MzgiLCJzdWJzY3JpcHRpb25Ob3RpZmljYXRpb24iOnsidmVyc2lvbiI6IjEuMCIsIm5vdGlmaWNhdGlvblR5cGUiOjEzLCJwdXJjaGFzZVRva2VuIjoiamdlZmZia29iY2RtZGhiZWlub2FiamVqLkFPLUoxT3o3V2w0RlpwRXFHdmlqVU9aN05LMFhEbGh6MkpWbTFTdVJYTTk3QlI2eHhoM201QmVndjhycTVDN0dJWTNxTUZzUnNWeTB5a3NyM2F1M2JvVnl1YWJ2M25BVXdRIiwic3Vic2NyaXB0aW9uSWQiOiJwcmVtaXVtX21vbnRobHkifX0=","messageId":"7163869041675931","message_id":"7163869041675931","publishTime":"2023-03-14T09:31:47.766Z","publish_time":"2023-03-14T09:31:47.766Z"},"subscription":"projects/start7-ca512/subscriptions/firstInAppPurchasesTopic-sub"}
        String logMessage;
        ObjectMapper mapper;
        String decodedData;
        try {
            // get the encoded notification data
            mapper = new ObjectMapper();
            JsonNode node = mapper.readTree(body);
            String message = node.get("message").toString();

            mapper = new ObjectMapper();
            String encodedData = mapper.readTree(message).get("data").asText();
            log.info("encodedData: " + encodedData);

            // decode the base64 notification data
            byte[] decodedBytes = Base64.getDecoder().decode(encodedData);
            decodedData = new String(decodedBytes);
        } catch (Exception e) {
            logMessage = "Failed to decode Google purchase notification. Skipping..";
            log.error(logMessage + " [" + body + "]");
            logMessageService.logException("receiveGoogleInAppPurchaseNotification", logMessage, e);
            return;
        }

        try {
            // parse the notification data
            mapper = new ObjectMapper();
            GoogleDeveloperNotificationDo developerNotification = mapper.readValue(decodedData, GoogleDeveloperNotificationDo.class);
            GoogleSubscriptionNotificationDo subscriptionNotification = developerNotification.getSubscriptionNotification();
            Integer notificationType = subscriptionNotification.getNotificationType();

            // if the android publisher could not be initialized and by this the purchase details not retrieved, the
            // notification should be saved in the table pending purchase notification
            if (handleAndroidPublisherNotExists(body, publisher, subscriptionNotification)) return;

            if (Objects.nonNull(notificationType) && GoogleNotificationType.getAllNumbers().contains(notificationType)) {
                String purchaseToken = subscriptionNotification.getPurchaseToken();
                log.info("Received Google notification with purchase token: " + purchaseToken);
                // get purchase details
                SubscriptionPurchase purchaseDetails = getPurchaseDetails(publisher, developerNotification.getPackageName(), subscriptionNotification.getSubscriptionId(), purchaseToken);
                String userId = purchaseDetails.getObfuscatedExternalAccountId();
                LocalDateTime expiryDate =
                        LocalDateTime.ofInstant(Instant.ofEpochMilli(purchaseDetails.getExpiryTimeMillis()), ZoneId.systemDefault());
                GoogleNotificationType googleNotificationType = GoogleNotificationType.findByNumber(notificationType);
                if (Objects.nonNull(userId)) {
                    log.info("received In-App-purchase notification with type: " + googleNotificationType.name());
                    if (googleNotificationType.name().equals(GoogleNotificationType.SUBSCRIPTION_PURCHASED.name())) {
                        upgradeUserToPremium(userId, expiryDate, subscriptionNotification, notificationType, purchaseDetails);
                    } else if (googleNotificationType.name().equals(GoogleNotificationType.SUBSCRIPTION_RENEWED.name())) {
                        upgradeUserToPremium(userId, expiryDate, subscriptionNotification, notificationType, purchaseDetails);
                    } else if (googleNotificationType.name().equals(GoogleNotificationType.SUBSCRIPTION_RECOVERED.name())) {
                        upgradeUserToPremium(userId, expiryDate, subscriptionNotification, notificationType, purchaseDetails);
                    } else if ((googleNotificationType.name().equals(GoogleNotificationType.SUBSCRIPTION_EXPIRED.name()))) {
                        Optional<String> tokensByUserIdOpt = userPurchaseNotificationRepository.findTokenByUserId(Util.convertId(userId)).stream().findFirst();
                        if (tokensByUserIdOpt.isPresent()) {
                            if (tokensByUserIdOpt.get().equals(purchaseToken)) {
                                userProfileService.updatePremiumStatus(Util.convertId(userId), false, expiryDate);
                            } else {
                                log.info("Received premium downgrade request for user id[" + userId + "] with purchase token [" + purchaseToken + "], although it is not equal to last user purchase token [" + tokensByUserIdOpt.get() + "]. skipping...");
                            }
                        } else {
                            log.info("Received premium downgrade request for user id[" + userId + "] with purchase token [" + purchaseToken + "], although the last user purchase is not found. skipping...");
                        }
                    }
                } else {
                    log.info("received Out-of-App-purchase notification with type: " + googleNotificationType.name());
                    // Out-of-App purchases are those made outside the App. That's why they don't have and external
                    // account-id and could not be assigned to a user profile.
                    // Out-of-App purchases are saved in separate table pending_purchase_notification: The App (Frontend) can get from the play store
                    // all purchase tokens of the currently logged user. By sending these tokens and the user id as parameters, the backend
                    // can query the table pending_purchase_notification and update the premium status accordingly
                    pendingPurchaseNotificationRepository.save(new HmPendingPurchaseNotification(purchaseToken, subscriptionNotification.getNotificationType(), purchaseDetails.getOrderId(), expiryDate));
                }
            }
        } catch (Exception e) {
            logMessage = "Failed to parse Google purchase notification data. Skipping..";
            log.error(logMessage + " [" + decodedData + "]");
            logMessageService.logException("receiveGoogleInAppPurchaseNotification", logMessage, e);
        }
    }

    private void upgradeUserToPremium(String userId, LocalDateTime expiryDate, GoogleSubscriptionNotificationDo subscriptionNotification, Integer notificationType, SubscriptionPurchase purchaseDetails) throws FormatException, EntityNotExistException {
        // By Android notifications save the last purchase token in order to be able to assign the EXPIRED status to the last user purchase
        try{
            if (userProfileService.updatePremiumStatus(Util.convertId(userId), true, expiryDate)) {
                // Update subscription info
                log.info("Processing android premium update for user id[" + userId + "], notificationType [" + notificationType + "], purchaseDetails [" + purchaseDetails + "]");
                HmSubscriptionInfo.SubscriptionType subscriptionType = determineSubscriptionType(subscriptionNotification.getSubscriptionId());

                log.info("Determining subscription details - type [" + subscriptionType + "]");

                LocalDateTime startDate =
                        LocalDateTime.ofInstant(Instant.ofEpochMilli(purchaseDetails.getStartTimeMillis()),
                                ZoneId.systemDefault());

                HmSubscriptionInfo subscription = subscriptionInfoService.createOrUpdateSubscription(

                        userId,
                        subscriptionNotification.getSubscriptionId(),
                        subscriptionType,
                        startDate,
                        null, // end date is null for active subscriptions
                        HmSubscriptionInfo.Platform.GOOGLE
                );

                // Update payment information
                if (purchaseDetails.getPriceAmountMicros() != null) {
                    BigDecimal amount = BigDecimal.valueOf(purchaseDetails.getPriceAmountMicros() / 1_000_000.0);
                    log.info("updating payment info - amount: [" + amount + "], orderId: [" + purchaseDetails.getOrderId() + "]");
                    subscriptionInfoService.updatePaymentInfo(
                            subscription.getId(),
                            amount,
                            LocalDateTime.now(),
                            purchaseDetails.getOrderId()
                    );
                }

                if (userPurchaseNotificationRepository.existsByUserId(Util.convertId(userId))) {
                    int rows = userPurchaseNotificationRepository.updatePurchaseInfoByUserId(subscriptionNotification.getPurchaseToken(), notificationType, expiryDate, Util.convertId(userId));
                    if (rows == 0) {
                        String message = "Failed to update user purchase info for user id[" + userId + "], skipping...";
                        log.info(message);
                        logMessageService.logException("AndroidNotificationService:upgradeUserToPremium", new TechnicalException(DATABASE_UPDATE_FAILED, message));
                    }
                } else {
                    userPurchaseNotificationRepository.save(new HmUserPurchaseNotification(userProfileService.getByIdInNewTransaction(userId), subscriptionNotification.getPurchaseToken(), notificationType, expiryDate));
                }
            }
            else {
                log.error("failed to update premium status for user ["+userId+"]");
                logMessageService.logWarning("AndroidNotificationService","failed to update premium status for user ["+userId+"]");
            }
        } catch (Exception e) {
            log.error("failed to update user purchase notification data. Skipping.", e);
            throw e;
        }
    }

    private boolean handleAndroidPublisherNotExists(String body, AndroidPublisher publisher, GoogleSubscriptionNotificationDo subscriptionNotification) {
        String logMessage;
        if (isNull(publisher)) {
            logMessage = "Failed to handle Google purchase notification. Reason: AndroidPublisher not initialized. Skipping..";
            log.error(logMessage + " [" + body + "]");
            logMessageService.logException("receiveGoogleInAppPurchaseNotification", logMessage,
                    new TechnicalException(GOOGLE_IN_APP_PURCHASES, "AndroidPublisher not initialized"));
            pendingPurchaseNotificationRepository.save(new HmPendingPurchaseNotification(subscriptionNotification.getPurchaseToken(), subscriptionNotification.getNotificationType()));
            return true;
        }
        return false;
    }

    public SubscriptionPurchase getPurchaseDetails(AndroidPublisher publisher, String packageName, String subscriptionId, String token) throws IOException {
        return publisher.purchases().subscriptions().get(packageName, subscriptionId, token).execute();
    }

    private HmSubscriptionInfo.SubscriptionType determineSubscriptionType(String subscriptionId) {
        // Implementation depends on subscription ID format
        return subscriptionId.toLowerCase().contains("monthly") ?
                HmSubscriptionInfo.SubscriptionType.MONTHLY :
                HmSubscriptionInfo.SubscriptionType.YEARLY;
    }

}
