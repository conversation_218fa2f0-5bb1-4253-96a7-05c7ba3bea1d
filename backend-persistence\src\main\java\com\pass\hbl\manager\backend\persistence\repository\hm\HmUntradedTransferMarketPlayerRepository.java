package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUntradedTransferMarketPlayer;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmUntradedTransferMarketPlayerRepository extends PagingAndSortingRepository<HmUntradedTransferMarketPlayer, UUID> {

    List<HmUntradedTransferMarketPlayer> findByPlayerIdAndCreatedAtAfterAndCreatedAtBefore(UUID playerId, LocalDateTime from, LocalDateTime to);
}
