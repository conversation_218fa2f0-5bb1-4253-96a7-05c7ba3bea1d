package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueInvitationDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueInvitation;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface HmLeagueInvitationRepository extends PagingAndSortingRepository<HmLeagueInvitation, UUID> {

    @Modifying
    @Query(value = "update HmLeagueInvitation m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where m.league.id = :leagueId and m.deleted = false")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    //Projections used with custom queries for performance & concurrency reasons: specific columns are returned instead of returning the whole entity within the transactional context
    @Query(value = "select l.id as id, l.league.id as leagueId, l.validUntil as validUntil from HmLeagueInvitation l where l.id = :id and l.deleted = false")
    Optional<HmLeagueInvitationDO> findLeagueMembershipInfoById(@Param("id") UUID id);

    @Query(value = "SELECT DISTINCT Cast(u.owner_id as varchar) from hm.league_invitation u where  u.owner_id IS NOT NULL and u.created_at > :changedAfter", nativeQuery = true)
    List<String> findAllUserWithLeagueInvitationActivityAfter(@Param("changedAfter") LocalDateTime changedAfter);
}
