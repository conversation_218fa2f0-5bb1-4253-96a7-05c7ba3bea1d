package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmSubscriptionInfo;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmSubscriptionInfoRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
@Transactional
public class SubscriptionInfoService {

    private final HmSubscriptionInfoRepository subscriptionInfoRepository;
    private final UserProfileService userProfileService;

    public SubscriptionInfoService(HmSubscriptionInfoRepository subscriptionInfoRepository,
                                   UserProfileService userProfileService) {
        this.subscriptionInfoRepository = subscriptionInfoRepository;
        this.userProfileService = userProfileService;
    }

    public HmSubscriptionInfo createOrUpdateSubscription(
            String userId,
            String subscriptionId,
            HmSubscriptionInfo.SubscriptionType type,
            HmSubscriptionInfo.SubscriptionPeriod period,
            LocalDateTime startDate,
            LocalDateTime endDate,
            HmSubscriptionInfo.Platform platform) throws EntityNotExistException, FormatException {

        UUID userUuid = Util.convertId(userId);
        HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);

        return subscriptionInfoRepository.findByUserIdAndSubscriptionId(userUuid, subscriptionId)
                .map(subscription -> {
                    subscription.setSubscriptionType(type);
                    subscription.setSubscriptionPeriod(period);
                    subscription.setStartDate(startDate);
                    subscription.setEndDate(endDate);
                    subscription.setPlatform(platform);
                    return subscriptionInfoRepository.save(subscription);
                })
                .orElseGet(() -> {
                    HmSubscriptionInfo newSubscription = new HmSubscriptionInfo();
                    newSubscription.setUser(user);
                    newSubscription.setSubscriptionId(subscriptionId);
                    newSubscription.setSubscriptionType(type);
                    newSubscription.setSubscriptionPeriod(period);
                    newSubscription.setStartDate(startDate);
                    newSubscription.setEndDate(endDate);
                    newSubscription.setPlatform(platform);
                    return subscriptionInfoRepository.save(newSubscription);
                });
    }

    public void updatePaymentInfo(UUID subscriptionId, BigDecimal amount, LocalDateTime paymentDate, String paymentId) {
        subscriptionInfoRepository.updatePaymentInfo(subscriptionId, amount, paymentDate, paymentId);
    }

    public void updateEndDate(UUID subscriptionId, LocalDateTime endDate) {
        subscriptionInfoRepository.updateEndDate(subscriptionId, endDate);
    }
}