package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserStatisticsDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.UserStatisticsActivityHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.UserStatisticsHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class UserStatisticsService {

    private final UserStatisticsActivityHandler userStatisticsActivityHandler;
    private final UserStatisticsHandler userStatisticsHandler;


    public UserStatisticsService(UserStatisticsActivityHandler userStatisticsActivityHandler, UserStatisticsHandler userStatisticsHandler) {
        this.userStatisticsActivityHandler = userStatisticsActivityHandler;
        this.userStatisticsHandler = userStatisticsHandler;
    }


    @SneakyThrows
    @Transactional(readOnly = true)
    public List<UserStatisticsDto> getAllUserStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws RateLimitExceededException {
        return userStatisticsHandler.getAllUserStatistics(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllUsersWithUpdatedLineup(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllUsersWithUpdatedLineup(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllUsersWithTransferMarketActivity(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllUsersWithTransferMarketActivity(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllUsersWithPlayerSales(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllUsersWithPlayerSales(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllUserWithSubmittedOffer(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllUserWithSubmittedOffer(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllDeletedUserAccounts(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllDeletedUserAccounts(changedAfter, pageable, requestUrl, externalClient);
    }

    @Transactional(readOnly = true)
    public List<String> getAllUserWithLeagueInvitationActivity(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, EntityNotExistException {
        return userStatisticsActivityHandler.getAllUserWithLeagueInvitationActivity(changedAfter, pageable, requestUrl, externalClient);
    }
}
