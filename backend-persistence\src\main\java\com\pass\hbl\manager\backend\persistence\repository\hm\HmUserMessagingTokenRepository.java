package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserTokenDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserMessagingToken;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface HmUserMessagingTokenRepository extends PagingAndSortingRepository<HmUserMessagingToken, UUID> {

    Optional<HmUserMessagingToken> findFirstByUserId(UUID uuid);

    @Query("select m.id from HmUserMessagingToken m where m.user.id = :userId and m.deleted = false")
    Set<UUID> findIdsByUserId(@Param("userId") UUID userId);

    @Query("select m.user.id as userId, m.token as token from HmUserMessagingToken m where m.user.id in :userIds and m.deleted = false")
    List<HmUserTokenDO> findTokensByUserIdIn(Set<UUID> userIds);


    @Modifying
    @Query("update HmUserMessagingToken m set m.token = :token, m.modifiedAt = CURRENT_TIMESTAMP where m.id = :id and deleted = false")
    int updateTokenById(@Param("token") String token, @Param("id") UUID id);

}
