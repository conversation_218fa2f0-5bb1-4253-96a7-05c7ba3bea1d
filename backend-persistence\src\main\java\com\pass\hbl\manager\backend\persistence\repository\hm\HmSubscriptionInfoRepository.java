package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.SubscriptionStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSubscriptionInfo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface HmSubscriptionInfoRepository extends PagingAndSortingRepository<HmSubscriptionInfo, UUID> {

    Optional<HmSubscriptionInfo> findByUserIdAndSubscriptionId(UUID userId, String subscriptionId);

    @Modifying
    @Query("UPDATE HmSubscriptionInfo s SET s.lastPaymentAmount = :amount, s.lastPaymentDate = :date, " +
            "s.lastPaymentId = :paymentId, s.modifiedAt = CURRENT_TIMESTAMP WHERE s.id = :id")
    int updatePaymentInfo(@Param("id") UUID id, @Param("amount") BigDecimal amount,
                          @Param("date") LocalDateTime date, @Param("paymentId") String paymentId);

    @Query(value = """
       SELECT cast(s.id as varchar) AS id,
              u.sso_id AS userSsoId,
              s.subscription_id AS subscriptionId,
              s.start_date AS startDate,
              s.end_date AS endDate,
              s.subscription_type AS subscriptionType,
              s.subscription_period AS subscriptionPeriod,
              s.platform AS platform,
              s.last_payment_id AS lastPaymentId,
              s.last_payment_amount AS lastPaymentAmount,
              s.last_payment_date AS lastPaymentDate
       FROM hm.subscription_info s
       INNER JOIN hm.user_profile u ON s.user_id = u.id
       WHERE s.deleted = false
       AND u.deleted = false
       AND (:changedAfter IS NULL OR s.modified_at >= :changedAfter)
       ORDER BY s.modified_at DESC
       """, nativeQuery = true)
    List<SubscriptionStatisticsBasicDO> findSubscriptionStatistics(@Param("changedAfter") LocalDateTime changedAfter);

    @Query(value = """
       SELECT cast(s.id as varchar) AS id,
              u.sso_id AS userSsoId,
              s.subscription_id AS subscriptionId,
              s.start_date AS startDate,
              s.end_date AS endDate,
              s.subscription_type AS subscriptionType,
              s.subscription_period AS subscriptionPeriod,
              s.platform AS platform,
              s.last_payment_id AS lastPaymentId,
              s.last_payment_amount AS lastPaymentAmount,
              s.last_payment_date AS lastPaymentDate
       FROM hm.subscription_info s
       INNER JOIN hm.user_profile u ON s.user_id = u.id
       WHERE s.deleted = false
       AND u.deleted = false
       AND s.id IN :ids
       ORDER BY s.modified_at DESC
       """, nativeQuery = true)
    List<SubscriptionStatisticsBasicDO> findSubscriptionStatisticsByIdIn(@Param("ids") List<UUID> ids);

    @Modifying
    @Query("UPDATE HmSubscriptionInfo s SET s.endDate = :endDate, s.modifiedAt = CURRENT_TIMESTAMP " +
            "WHERE s.id = :id")
    int updateEndDate(@Param("id") UUID id, @Param("endDate") LocalDateTime endDate);
}