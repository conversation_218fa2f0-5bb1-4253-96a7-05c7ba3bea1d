package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.util.Arrays;
import java.util.List;

public enum GoogleNotificationType {
    SUBSCRIPTION_RECOVERED(1, "SUBSCRIPTION_RECOVERED"),
    SUBSCRIPTION_RENEWED(2, "SUBSCRIPTION_RENEWED"),
    SUBSCRIPTION_PURCHASED(4, "SUBSCRIPTION_PURCHASED"),
    SUBSCRIPTION_EXPIRED(13, "SUBSCRIPTION_EXPIRED");

    private final int number;
        private final String description;

    GoogleNotificationType(int number, String stringValue) {
            this.number = number;
            this.description = stringValue;
        }

        public int getNumber() {
            return number;
        }

        public String getDescription() {
            return description;
        }

        public static GoogleNotificationType findByNumber(int number) {
            for (GoogleNotificationType notificationType : GoogleNotificationType.values()) {
                if (notificationType.number == number) {
                    return notificationType;
                }
            }
            throw new IllegalArgumentException("Invalid number: " + number);
        }

        public static List<Integer> getAllNumbers() {
            return Arrays.stream(GoogleNotificationType.values()).map(t -> t.number).toList();
        }
}
