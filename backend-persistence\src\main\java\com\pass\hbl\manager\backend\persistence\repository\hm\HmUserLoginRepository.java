package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserLogin;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for user login tracking
 */
public interface HmUserLoginRepository extends PagingAndSortingRepository<HmUserLogin, UUID> {

    @Query("SELECT u FROM HmUserLogin u WHERE u.user.id = :userId")
    Optional<HmUserLogin> findByUserId(@Param("userId") UUID userId);

    /**
     * Update the last login date for a user by user ID
     *
     * @param lastLogin New last login date
     * @param userId User ID
     */
    @Modifying
    @Query("UPDATE HmUserLogin u SET u.lastLogin = :lastLogin WHERE u.user.id = :userId")
    void updateLastLogin(@Param("lastLogin") LocalDate lastLogin, @Param("userId") UUID userId);
}
