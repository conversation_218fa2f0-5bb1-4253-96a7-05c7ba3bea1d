package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Subscription statistics data transfer object
 * Contains comprehensive subscription and payment data for statistics API endpoint
 */
@JsonRootName("SubscriptionStatistics")
@Getter
@Setter
@Schema(description = "Subscription statistics with comprehensive subscription and payment data")
public class SubscriptionStatisticsDto {

    @Schema(description = "User SSO id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    private String userSsoId;

    @Schema(description = "Subscription ID", example = "monthly_premium_001", required = true)
    @NotBlank
    private String subscriptionId;

    @Schema(description = "Subscription start date and time", required = true)
    private LocalDateTime subscriptionStartDate;

    @Schema(description = "Subscription end date and time (only set if subscription was actively terminated by user)")
    private LocalDateTime subscriptionEndDate;

    @Schema(description = "Subscription type", example = "MONTHLY", required = true)
    private String subscriptionType;

    @Schema(description = "Subscription period", example = "MONTHLY", required = true)
    private String subscriptionPeriod;

    @Schema(description = "Platform where subscription was purchased", example = "GOOGLE")
    private String platform;

    @Schema(description = "Payment ID", example = "GPA.1234-5678-9012-34567")
    private String paymentId;

    @Schema(description = "Payment amount", example = "4.99")
    private BigDecimal paymentAmount;

    @Schema(description = "Payment date and time")
    private LocalDateTime paymentDate;

    @Schema(description = "Filter date for changed after query", example = "2024-02-20T15:30:00")
    private LocalDateTime changedAfter;

    @JsonCreator
    public SubscriptionStatisticsDto() {
    }

    /**
     * Constructor with essential fields
     */
    public SubscriptionStatisticsDto(String userSsoId, String subscriptionId, LocalDateTime subscriptionStartDate, 
                                   String subscriptionType, String subscriptionPeriod) {
        this.userSsoId = userSsoId;
        this.subscriptionId = subscriptionId;
        this.subscriptionStartDate = subscriptionStartDate;
        this.subscriptionType = subscriptionType;
        this.subscriptionPeriod = subscriptionPeriod;
        this.changedAfter = null;
    }
}
