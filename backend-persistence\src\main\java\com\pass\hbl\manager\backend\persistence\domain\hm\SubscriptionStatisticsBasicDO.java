package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Domain object for basic subscription statistics data
 * Contains only the fields needed for subscription statistics
 */
public interface SubscriptionStatisticsBasicDO {
    
    /**
     * Get the subscription ID
     * @return Subscription ID
     */
    String getId();
    
    /**
     * Get the user SSO ID
     * @return User SSO ID
     */
    String getUserSsoId();
    
    /**
     * Get the subscription ID
     * @return Subscription ID
     */
    String getSubscriptionId();
    
    /**
     * Get the subscription start date
     * @return Subscription start date
     */
    LocalDateTime getStartDate();
    
    /**
     * Get the subscription end date
     * @return Subscription end date (null if not terminated)
     */
    LocalDateTime getEndDate();
    
    /**
     * Get the subscription type
     * @return Subscription type (PLUS, WORLD)
     */
    String getSubscriptionType();
    
    /**
     * Get the subscription period
     * @return Subscription period (MONTHLY, ANNUALLY)
     */
    String getSubscriptionPeriod();
    
    /**
     * Get the platform
     * @return Platform (GOOGLE, APPLE, etc.)
     */
    String getPlatform();
    
    /**
     * Get the last payment ID
     * @return Last payment ID
     */
    String getLastPaymentId();
    
    /**
     * Get the last payment amount
     * @return Last payment amount
     */
    BigDecimal getLastPaymentAmount();
    
    /**
     * Get the last payment date
     * @return Last payment date
     */
    LocalDateTime getLastPaymentDate();
}
